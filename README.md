# Easy Social Share Example

This example app demonstrates how to use the [easy_social_share](../README.md) Flutter plugin to share content to various social media platforms and messaging apps.

## Features Demonstrated

The example app showcases the following sharing capabilities:

### Android Platform
- **WhatsApp**: Share text messages and files (single or multiple)
- **Telegram**: Share text messages and files (single or multiple)
- **Twitter**: Share text messages with optional file attachments
- **Instagram**:
  - Direct messages
  - Feed posts (single or multiple images)
  - Reels (videos)
  - Stories (with customizable backgrounds and stickers)
- **Facebook**:
  - Feed posts with hashtags and files
  - Stories (with customizable backgrounds and stickers)
- **TikTok**: Share content to TikTok status (Android only)
- **Messenger**: Share text messages
- **SMS**: Share text messages and files (single or multiple)
- **System Share**: Use the native Android share dialog
- **Clipboard**: Copy text to clipboard

### Cross-Platform
- **Get Installed Apps**: Check which social media apps are installed on the device

## Getting Started

### Prerequisites
- Flutter SDK (>=3.32.4)
- Dart SDK (>=3.0.0 <4.0.0)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/tentram/flutter_packages.git
cd flutter_packages/packages/easy_social_share/example
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the example app:
```bash
flutter run
```

## Usage Example

The main example demonstrates sharing an image to the system share dialog:

```dart
import 'package:easy_social_share/easy_social_share.dart';
import 'package:file_picker/file_picker.dart';

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  EasySocialShare easySocialShare = EasySocialShare();

  Future<void> _shareToSystem(String message, String filePath) async {
    final String result = await easySocialShare.android.shareToSystem(
      'Share Image',
      message,
      filePath,
    );
    print('Share result: $result');
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: ElevatedButton(
            child: Text("Share to System"),
            onPressed: () async {
              FilePickerResult? result = await FilePicker.platform.pickFiles(
                type: FileType.image,
                allowMultiple: false,
              );

              if (result != null && result.paths.isNotEmpty) {
                _shareToSystem("Check out this image!", result.paths[0]!);
              }
            },
          ),
        ),
      ),
    );
  }
}
```

## Platform Support

| Platform | Supported |
|----------|-----------|
| Android  | ✅        |
| iOS      | ✅        |

## Dependencies

- `flutter`: Flutter SDK
- `easy_social_share`: The main plugin (path dependency to parent directory)
- `file_picker`: For selecting files to share
- `cupertino_icons`: iOS-style icons

## API Reference

### Basic Usage

```dart
import 'package:easy_social_share/easy_social_share.dart';

EasySocialShare easySocialShare = EasySocialShare();

// Check installed apps
Map<String, bool> apps = await easySocialShare.getInstalledApps();

// Share to WhatsApp (Android)
await easySocialShare.android.shareToWhatsapp("Hello!", "/path/to/image.jpg");

// Share to system (iOS)
await easySocialShare.iOS.shareToSystem("Hello!", filePaths: ["/path/to/image.jpg"]);
```

### Available Methods

#### Platform Usage

**Android**: Access methods via `easySocialShare.android.methodName()`
- Supports file sharing for most platforms
- Supports multiple file sharing for WhatsApp, Telegram, Instagram Feed, SMS, and System Share
- Includes TikTok Status sharing (Android-only feature)

**iOS**: Access methods via `easySocialShare.iOS.methodName()`
- Limited file sharing capabilities compared to Android
- WhatsApp requires separate methods for text (`shareToWhatsapp`) and images (`shareImageToWhatsApp`)
- Includes TikTok Post sharing (requires additional setup)
- No multiple file sharing support

#### Unified Methods Table

| Platform | Method | Android Parameters | iOS Parameters | Multiple Files Support |
|----------|--------|-------------------|----------------|----------------------|
| **Cross-Platform** | `getInstalledApps()` | - | - | Returns `Map<String, bool>` |
| **WhatsApp** | `shareToWhatsapp()` | message, filePath? | message (text only) | Android: `shareFilesToWhatsapp()` |
| | `shareImageToWhatsApp()` | ❌ | filePath | ❌ |
| **Telegram** | `shareToTelegram()` | message, filePath? | message (text only) | Android: `shareFilesToTelegram()` |
| **Twitter** | `shareToTwitter()` | message, filePath? | message, filePath? | ❌ |
| **Instagram Direct** | `shareToInstagramDirect()` | message | message | ❌ |
| **Instagram Feed** | `shareToInstagramFeed()` | message, filePath? | imagePath | Android: `shareFilesToInstagramFeed()` |
| **Instagram Reels** | `shareToInstagramReels()` | videoPaths (List) | videoPath (String) | ❌ |
| **Instagram Story** | `shareToInstagramStory()` | appId + story options* | appId + story options* | ❌ |
| **Facebook** | `shareToFacebook()` | hashtag, filePaths | hashtag, filePaths | ❌ |
| **Facebook Story** | `shareToFacebookStory()` | appId + story options* | appId + story options* | ❌ |
| **TikTok** | `shareToTiktokStatus()` | filePaths | ❌ (Android only) | ❌ |
| | `shareToTiktokPost()` | ❌ (iOS only) | videoFile, redirectUrl, fileType | ❌ |
| **Messenger** | `shareToMessenger()` | message | message | ❌ |
| **SMS** | `shareToSMS()` | message, filePath? | message (text only) | Android: `shareFilesToSMS()` |
| **System Share** | `shareToSystem()` | title, message, filePath? | message, filePaths? | Android: `shareFilesToSystem()` |
| **Clipboard** | `copyToClipBoard()` | message | message | ❌ |

*Story options: `stickerImage`, `backgroundImage`, `backgroundVideo`, `backgroundTopColor`, `backgroundBottomColor`, `attributionURL`

### Platform Differences

| Feature | Android | iOS |
|---------|---------|-----|
| File sharing | ✅ Most apps | ⚠️ Limited |
| Multiple files | ✅ | ❌ |
| TikTok Status | ✅ | ❌ |
| TikTok Post | ❌ | ✅ (requires setup) |

### Quick Examples

```dart
// Check if WhatsApp is installed
Map<String, bool> apps = await easySocialShare.getInstalledApps();
if (apps['whatsapp'] == true) {
  // Share to WhatsApp
}

// Share image to Instagram Story
await easySocialShare.android.shareToInstagramStory(
  'your_app_id',
  backgroundImage: '/path/to/image.jpg',
);

// Share multiple images (Android only)
await easySocialShare.android.shareFilesToWhatsapp([
  '/path/to/image1.jpg',
  '/path/to/image2.jpg',
]);
```

## Additional Examples

For more detailed examples of sharing to specific platforms, check out the [main plugin documentation](../README.md).

## Contributing

This example is part of the easy_social_share plugin package. For contributions and issues, please visit the [main repository](https://github.com/tentram/flutter_packages).

## License

This example follows the same license as the main easy_social_share plugin.
